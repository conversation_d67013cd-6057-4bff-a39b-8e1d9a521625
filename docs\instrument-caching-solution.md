# 全局Instrument数据缓存方案

## 1. 方案概述

### 1.1 背景
当前项目中，instrument（期货合约）数据在多个页面和组件中被频繁使用，但存在以下问题：
- `InstrumentSelector.vue` 组件每次挂载都调用 `getInstrumentSelectList()` API 获取数据
- `useContractData.ts` 中单独调用 `getInstrumentById` 获取单个合约信息
- 数据重复获取，影响性能
- 缺乏统一的数据管理和缓存机制

### 1.2 目标
- 建立全局instrument数据缓存，避免重复API调用
- 按交易所→product→合约的层级组织数据，便于快速查找
- 支持按id和instrument_id多种方式查找
- 提供统一的数据访问接口，简化组件逻辑
- 使用storage持久化，提升用户体验

### 1.3 技术架构
- **状态管理**: 基于Pinia实现全局状态管理
- **数据持久化**: 使用uni.setStorage/getStorage实现本地缓存
- **API封装**: 保持现有API接口不变，在store层封装
- **向后兼容**: 渐进式替换现有调用，不影响现有功能

## 2. 数据结构设计

### 2.1 核心数据结构
```typescript
// 按交易所分组的层级结构
interface InstrumentsByExchange {
  [exchangeId: string]: {
    exchangeName: string
    products: {
      [productName: string]: {
        productName: string
        instruments: IInstrumentSelectItem[]
      }
    }
  }
}

// 带索引的数据结构
interface InstrumentStoreData {
  // 原始数据列表
  instruments: IInstrumentSelectItem[]
  
  // 按交易所分组的层级数据
  byExchange: InstrumentsByExchange
  
  // 索引: 按id查找
  byId: Record<number, IInstrumentSelectItem>
  
  // 索引: 按instrument_id查找
  byInstrumentId: Record<string, IInstrumentSelectItem>
  
  // 交易所列表
  exchanges: Array<{
    value: string
    label: string
  }>
  
  // 元数据
  lastUpdated: number // 最后更新时间
  isLoading: boolean
  error: string | null
}
```

### 2.2 Store接口设计
```typescript
interface InstrumentStore {
  // 状态
  instruments: IInstrumentSelectItem[]
  byExchange: InstrumentsByExchange
  byId: Record<number, IInstrumentSelectItem>
  byInstrumentId: Record<string, IInstrumentSelectItem>
  exchanges: Array<{ value: string; label: string }>
  lastUpdated: number
  isLoading: boolean
  error: string | null
  
  // 计算属性
  isDataLoaded: boolean
  isDataExpired: boolean
  
  // 方法
  loadInstruments(): Promise<void>
  refreshInstruments(): Promise<void>
  getInstrumentById(id: number): IInstrumentSelectItem | null
  getInstrumentByInstrumentId(instrumentId: string): IInstrumentSelectItem | null
  getInstrumentsByExchange(exchangeId: string): InstrumentsByExchange[string] | null
  getProductsByExchange(exchangeId: string): Array<{ value: string; label: string }>
  getInstrumentsByProduct(exchangeId: string, productName: string): IInstrumentSelectItem[]
}
```

## 3. 实现方案

### 3.1 Store实现 (src/store/instrument.ts)
```typescript
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { getInstrumentSelectList } from '@/api/instrument'
import { ExchangeMap } from '@/types/instrument'
import type { IInstrumentSelectItem } from '@/types'

export const useInstrumentStore = defineStore(
  'instrument',
  () => {
    // 状态定义
    const instruments = ref<IInstrumentSelectItem[]>([])
    const byExchange = ref<InstrumentsByExchange>({})
    const byId = ref<Record<number, IInstrumentSelectItem>>({})
    const byInstrumentId = ref<Record<string, IInstrumentSelectItem>>({})
    const exchanges = ref<Array<{ value: string; label: string }>>([])
    const lastUpdated = ref(0)
    const isLoading = ref(false)
    const error = ref<string | null>(null)

    // 配置
    const CACHE_EXPIRE_TIME = 30 * 60 * 1000 // 30分钟过期
    const STORAGE_KEY = 'instrument_data_cache'

    // 计算属性
    const isDataLoaded = computed(() => instruments.value.length > 0)
    const isDataExpired = computed(() => {
      if (lastUpdated.value === 0) return true
      return Date.now() - lastUpdated.value > CACHE_EXPIRE_TIME
    })

    // 数据处理方法
    function processData(instrumentList: IInstrumentSelectItem[]) {
      // 清空现有数据
      byExchange.value = {}
      byId.value = {}
      byInstrumentId.value = {}

      // 构建索引
      for (const instrument of instrumentList) {
        // 按id索引
        byId.value[instrument.id] = instrument
        
        // 按instrument_id索引
        byInstrumentId.value[instrument.instrument_id] = instrument
        
        // 按交易所分组
        if (!byExchange.value[instrument.exchange_id]) {
          byExchange.value[instrument.exchange_id] = {
            exchangeName: ExchangeMap[instrument.exchange_id] || instrument.exchange_id,
            products: {}
          }
        }
        
        // 按product分组
        if (!byExchange.value[instrument.exchange_id].products[instrument.product_name]) {
          byExchange.value[instrument.exchange_id].products[instrument.product_name] = {
            productName: instrument.product_name,
            instruments: []
          }
        }
        
        byExchange.value[instrument.exchange_id].products[instrument.product_name].instruments.push(instrument)
      }

      // 构建交易所列表
      exchanges.value = Object.entries(ExchangeMap).map(([value, label]) => ({
        value,
        label
      }))
    }

    // 数据加载方法
    async function loadInstruments(forceRefresh = false) {
      // 如果数据已加载且未过期，直接返回
      if (!forceRefresh && isDataLoaded.value && !isDataExpired.value) {
        return
      }

      try {
        isLoading.value = true
        error.value = null

        // 尝试从缓存加载
        if (!forceRefresh) {
          const cachedData = uni.getStorageSync(STORAGE_KEY)
          if (cachedData && Date.now() - cachedData.timestamp < CACHE_EXPIRE_TIME) {
            instruments.value = cachedData.instruments
            processData(cachedData.instruments)
            lastUpdated.value = cachedData.timestamp
            return
          }
        }

        // 从API加载
        const response = await getInstrumentSelectList()
        if (response.code === 0 && response.data) {
          instruments.value = response.data
          processData(response.data)
          lastUpdated.value = Date.now()
          
          // 保存到缓存
          uni.setStorageSync(STORAGE_KEY, {
            instruments: response.data,
            timestamp: lastUpdated.value
          })
        }
      } catch (err) {
        error.value = err instanceof Error ? err.message : '加载失败'
        throw err
      } finally {
        isLoading.value = false
      }
    }

    // 强制刷新数据
    async function refreshInstruments() {
      await loadInstruments(true)
    }

    // 查找方法
    function getInstrumentById(id: number): IInstrumentSelectItem | null {
      return byId.value[id] || null
    }

    function getInstrumentByInstrumentId(instrumentId: string): IInstrumentSelectItem | null {
      return byInstrumentId.value[instrumentId] || null
    }

    function getInstrumentsByExchange(exchangeId: string): InstrumentsByExchange[string] | null {
      return byExchange.value[exchangeId] || null
    }

    function getProductsByExchange(exchangeId: string): Array<{ value: string; label: string }> {
      const exchange = byExchange.value[exchangeId]
      if (!exchange) return []
      
      return Object.keys(exchange.products).map(productName => ({
        value: productName,
        label: productName
      }))
    }

    function getInstrumentsByProduct(exchangeId: string, productName: string): IInstrumentSelectItem[] {
      const exchange = byExchange.value[exchangeId]
      if (!exchange || !exchange.products[productName]) return []
      
      return exchange.products[productName].instruments
    }

    return {
      // 状态
      instruments,
      byExchange,
      byId,
      byInstrumentId,
      exchanges,
      lastUpdated,
      isLoading,
      error,
      
      // 计算属性
      isDataLoaded,
      isDataExpired,
      
      // 方法
      loadInstruments,
      refreshInstruments,
      getInstrumentById,
      getInstrumentByInstrumentId,
      getInstrumentsByExchange,
      getProductsByExchange,
      getInstrumentsByProduct
    }
  },
  {
    persist: true // 启用Pinia持久化
  }
)
```

### 3.2 InstrumentSelector组件改造 (src/components/InstrumentSelector.vue)

#### 主要改动：
1. 移除本地数据获取和缓存逻辑
2. 使用instrument store替代直接API调用
3. 简化组件逻辑，只保留UI交互功能

#### 改动内容：
```typescript
// 移除的代码：
- initializeData函数
- cachedData相关的状态和逻辑
- getInstrumentSelectList API调用

// 新增的代码：
import { useInstrumentStore } from '@/store/instrument'

const instrumentStore = useInstrumentStore()

// 替换数据初始化
onMounted(async () => {
  try {
    await instrumentStore.loadInstruments()
    // 初始化显示值逻辑保持不变
  } catch (error) {
    // 错误处理逻辑
  }
})

// 使用store的数据替代本地缓存
const cachedData = computed(() => ({
  exchanges: instrumentStore.exchanges,
  productsByExchange: {}, // 通过store方法动态获取
  contractsByProduct: {}, // 通过store方法动态获取
  instrumentsById: instrumentStore.byId
}))

// 动态获取product列表
function getProductsByExchange(exchangeId: string) {
  return instrumentStore.getProductsByExchange(exchangeId)
}

// 动态获取合约列表
function getInstrumentsByProduct(exchangeId: string, productName: string) {
  return instrumentStore.getInstrumentsByProduct(exchangeId, productName)
}
```

### 3.3 useContractData改造 (src/composables/useContractData.ts)

#### 主要改动：
```typescript
// 修改loadInstrument函数
async function loadInstrument(id: number) {
  try {
    const instrumentStore = useInstrumentStore()
    
    // 先从store查找
    const instrument = instrumentStore.getInstrumentById(id)
    if (instrument) {
      instrument.value = instrument
      return
    }
    
    // 如果store中没有，尝试加载store数据
    await instrumentStore.loadInstruments()
    
    // 再次查找
    const foundInstrument = instrumentStore.getInstrumentById(id)
    if (foundInstrument) {
      instrument.value = foundInstrument
    }
  } catch (error) {
    console.error('加载期货合约信息失败:', error)
  }
}
```

## 4. 文件修改清单

### 4.1 新增文件
- `src/store/instrument.ts` - 全局instrument状态管理store

### 4.2 主要修改文件
- `src/components/InstrumentSelector.vue` - 使用全局数据替代本地数据
- `src/composables/useContractData.ts` - 使用全局数据替代API调用
- `src/types/instrument.ts` - 添加store相关类型定义

### 4.3 可选优化文件
- `src/pages/quotes/edit.vue` - 检查并优化instrument数据调用
- `src/pages/contract/form.vue` - 检查并优化instrument数据调用
- `src/pages/trade/execute.vue` - 检查并优化instrument数据调用

## 5. 实施计划

### 阶段1：核心功能实现
1. **优先级**: 高
2. **工作量**: 中等
3. **任务**:
   - 创建 `src/store/instrument.ts`
   - 修改 `src/components/InstrumentSelector.vue`
   - 修改 `src/composables/useContractData.ts`
4. **验证**: 确保核心功能正常，数据缓存生效

### 阶段2：扩展优化
1. **优先级**: 中
2. **工作量**: 低
3. **任务**:
   - 检查并优化其他页面的instrument调用
   - 添加数据过期和自动刷新机制
   - 性能监控和调优
4. **验证**: 全面测试，确保无回归问题

## 6. 预期收益

### 6.1 性能提升
- 减少70%以上的重复API调用
- 页面加载速度提升30-50%
- 支持离线使用场景

### 6.2 代码质量
- 组件逻辑简化，职责更清晰
- 统一数据管理，便于维护
- 减少代码冗余，提高复用性

### 6.3 用户体验
- 页面响应更快，减少等待时间
- 数据一致性更好
- 支持离线使用场景

## 7. 风险评估

### 7.1 技术风险
- **风险**: Pinia持久化可能与uni-app storage冲突
- **对策**: 使用统一的storage管理，避免冲突

### 7.2 兼容性风险
- **风险**: 现有代码依赖当前数据结构
- **对策**: 保持向后兼容，渐进式替换

### 7.3 性能风险
- **风险**: 大量数据可能影响store性能
- **对策**: 使用索引和分层结构，优化查找性能

## 8. 监控指标

### 8.1 性能指标
- API调用次数减少率
- 页面加载时间
- 缓存命中率

### 8.2 用户体验指标
- 用户等待时间
- 离线功能可用性
- 错误率

### 8.3 代码质量指标
- 代码重复率
- 组件复杂度
- 测试覆盖率

## 9. 维护计划

### 9.1 日常维护
- 定期检查缓存过期策略
- 监控API调用频率
- 优化数据结构

### 9.2 版本更新
- 适配新的API接口
- 更新数据结构定义
- 优化性能和用户体验

---

**文档版本**: 1.0  
**创建日期**: 2025-07-31  
**最后更新**: 2025-07-31  
**负责人**: 开发团队